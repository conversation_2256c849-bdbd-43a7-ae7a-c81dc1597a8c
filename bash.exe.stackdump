Stack trace:
Frame         Function      Args
0007FFFFABB0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFABB0, 0007FFFF9AB0) msys-2.0.dll+0x1FE8E
0007FFFFABB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE88) msys-2.0.dll+0x67F9
0007FFFFABB0  000210046832 (000210286019, 0007FFFFAA68, 0007FFFFABB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFABB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABB0  000210068E24 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAE90  00021006A225 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBC05A0000 ntdll.dll
7FFBBE820000 KERNEL32.DLL
7FFBBDCA0000 KERNELBASE.dll
7FFBBAA40000 apphelp.dll
7FFBBF0C0000 USER32.dll
7FFBBDB50000 win32u.dll
7FFBC0360000 GDI32.dll
7FFBBE2D0000 gdi32full.dll
7FFBBDB80000 msvcp_win.dll
7FFBBDA30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBBF320000 advapi32.dll
7FFBBF270000 msvcrt.dll
7FFBBF410000 sechost.dll
7FFBBE6F0000 RPCRT4.dll
7FFBBD1B0000 CRYPTBASE.DLL
7FFBBDC20000 bcryptPrimitives.dll
7FFBBF3D0000 IMM32.DLL
